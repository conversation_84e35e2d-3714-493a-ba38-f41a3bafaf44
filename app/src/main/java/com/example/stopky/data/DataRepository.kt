package com.example.stopky.data

import android.content.Context
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import kotlinx.coroutines.*
import android.util.Log

// Datové třídy

data class MaterialRow(
    @SerializedName("norma") val norma: String?, // Opraveno z "norma "
    @SerializedName("izotop") val izotop: String?,
    @SerializedName("material") val material: String?,
    @SerializedName("mu_mm") val mu: String? // Opraveno z "mu_mm "
)
data class IsotopeRow(
    @SerializedName("nazev") val nazev: String?, // Opraveno z "nazev "
    @SerializedName("polocas_dny") val polocas: String?, // Opraveno z "polocas_dny "
    @SerializedName("konst_48") val konst48: String?, // Opraveno z "konst_48 "
    @SerializedName("konst_52") val konst52: String?, // Změněno z Any? na String?
    @SerializedName("konst_59") val konst59: String?, // Změněno z Any? na String?
    @SerializedName("konst_14") val konst14: String?, // Změněno z Any? na String?
    @SerializedName("konst_22") val konst22: String?, // Přidáno pro Se75
    @SerializedName("jednotky") val jednotky: String? // Opraveno z "jednotky "
)
data class FilmRow(
    @SerializedName("nazev") val nazev: String?,
    // R-faktory s novým pojmenováním pro lepší mapování
    @SerializedName("r_faktor_10") val r_faktor_10: String?,
    @SerializedName("r_faktor_15") val r_faktor_15: String?,
    @SerializedName("r_faktor_20") val r_faktor_20: String?,
    @SerializedName("r_faktor_25") val r_faktor_25: String?,
    @SerializedName("r_faktor_30") val r_faktor_30: String?,
    @SerializedName("r_faktor_35") val r_faktor_35: String?,
    @SerializedName("r_faktor_40") val r_faktor_40: String?
)
data class HVLRow(
    @SerializedName("nazev") val nazev: String?,
    @SerializedName("hodnota") val hodnota: String? // Změněno z Any? na String? (pro "0.0" nebo prázdný string)
)
data class ObjectTypeRow(
    @SerializedName("nazev") val nazev: String?,
    @SerializedName("pravidlo_vypoctu") val pravidlo: String? // Opraveno z "pravidlo_vypoctu "
)
data class LocalizationRow(
    @SerializedName("key") val key: String?, // Opraveno z "key "
    @SerializedName("cs") val cs: String?, // Opraveno z "cs "
    @SerializedName("en") val en: String? // Opraveno z "en "
)
// Přidáno pro nové seznamy jednotek
data class UnitSystemRow(@SerializedName("name") val name: String?)
data class ActivityUnitRow(@SerializedName("name") val name: String?)
data class DoseUnitRow(@SerializedName("name") val name: String?)

data class ExposureDataJson(
    @SerializedName("Materials") val materials: List<MaterialRow>?,
    @SerializedName("Isotopes") val isotopes: List<IsotopeRow>?,
    @SerializedName("Films") val films: List<FilmRow>?,
    @SerializedName("HVL") val hvls: List<HVLRow>?,
    @SerializedName("ObjectTypes") val objectTypes: List<ObjectTypeRow>?,
    @SerializedName("Localization") val localization: List<LocalizationRow>?,
    // Přidáno pro nové seznamy jednotek
    @SerializedName("UnitSystems") val unitSystems: List<UnitSystemRow>?,
    @SerializedName("ActivityUnits") val activityUnits: List<ActivityUnitRow>?,
    @SerializedName("DoseUnits") val doseUnits: List<DoseUnitRow>?
)

class DataRepository(private val context: Context) {
    var materials: List<MaterialRow> = emptyList()
    var isotopes: List<IsotopeRow> = emptyList()
    var films: List<FilmRow> = emptyList()
    var hvls: List<HVLRow> = emptyList()
    var objectTypes: List<ObjectTypeRow> = emptyList()
    var localization: List<LocalizationRow> = emptyList()
    // Přidáno pro nové seznamy jednotek
    var unitSystems: List<UnitSystemRow> = emptyList()
    var activityUnits: List<ActivityUnitRow> = emptyList()
    var doseUnits: List<DoseUnitRow> = emptyList()

    // Načtení dat z JSON
    fun loadJsonData(assetFileName: String = "exposure_data.json") {
        val jsonString = context.assets.open(assetFileName).bufferedReader().use { it.readText() }
        val data = Gson().fromJson(jsonString, ExposureDataJson::class.java)
        materials = data.materials ?: emptyList()
        isotopes = data.isotopes ?: emptyList()
        films = data.films ?: emptyList()
        hvls = data.hvls ?: emptyList()
        objectTypes = data.objectTypes ?: emptyList()
        localization = data.localization ?: emptyList()
        // Přidáno pro nové seznamy jednotek
        unitSystems = data.unitSystems ?: emptyList()
        activityUnits = data.activityUnits ?: emptyList()
        doseUnits = data.doseUnits ?: emptyList()
    }
}
