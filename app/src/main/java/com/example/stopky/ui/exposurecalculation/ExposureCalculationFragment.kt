package com.example.stopky.ui.exposurecalculation

import android.app.DatePickerDialog
import android.content.SharedPreferences
import android.os.Bundle
import android.os.CountDownTimer
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.widget.AdapterView
import android.widget.ArrayAdapter
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.preference.PreferenceManager
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.launch
import com.example.stopky.R
import com.example.stopky.data.DataRepository
import com.example.stopky.databinding.FragmentExposureCalculationBinding
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale
import kotlin.math.exp
import kotlin.math.ln
import kotlin.math.pow // Přidáno pro Math.pow

class ExposureCalculationFragment : Fragment() {
    private var _binding: FragmentExposureCalculationBinding? = null
    private val binding get() = _binding!!
    private lateinit var dataRepo: DataRepository
    private val calendar = Calendar.getInstance() // Pro DatePicker a uložení počátečního data

    // Klíče pro SharedPreferences
    companion object {
        private const val PREF_INITIAL_ACTIVITY = "initial_activity"
        private const val PREF_INITIAL_DATE_MILLIS = "initial_date_millis"
        private const val PREF_SELECTED_ISOTOPE = "selected_isotope"

        // Nové klíče pro paměť všech parametrů
        private const val PREF_THICKNESS = "exposure_thickness"
        private const val PREF_WELD_REINFORCEMENT = "exposure_weld_reinforcement"
        private const val PREF_DISTANCE = "exposure_distance"
        private const val PREF_USER_FACTOR = "exposure_user_factor"
        private const val PREF_FILM_TYPE = "exposure_film_type"
        private const val PREF_DENSITY = "exposure_density"
        private const val PREF_NUM_EXPOSURES = "exposure_num_exposures"
        private const val PREF_BARRICADE_DOSE = "exposure_barricade_dose"

        // Konstanty pro převod jednotek
        private const val MM_PER_INCH = 25.4
        private const val MM_PER_FOOT = 304.8 // 12 * 25.4
        private const val METERS_PER_FOOT = 0.3048 // Přidáno zpět
        private const val GBQ_PER_CI = 37.0 // 1 Ci = 37 GBq
    }

    // Nové příznaky pro řízení manuálního nastavení aktivity
    private var isCurrentActivityManuallySet = false
    private var isUpdatingActivityProgrammatically = false // Zabraňuje rekurzivnímu volání TextWatcheru

    // Proměnné pro stopky/countdown
    private var countdownTimer: CountDownTimer? = null
    private var isTimerRunning = false
    private var remainingTimeSeconds = 0

    // Proměnná pro μ hodnotu (místo čtení z UI)
    private var currentMuValue: Double? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentExposureCalculationBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dataRepo = DataRepository(requireContext())

        // OPRAVA ANR: Asynchronní načítání JSON dat s loading indikátorem
        showLoadingState(true)
        lifecycleScope.launch {
            val success = dataRepo.loadJsonDataAsync()
            if (success) {
                initializeUI()
            } else {
                showErrorState()
            }
            showLoadingState(false)
        }
    }

    private fun initializeUI() {
        val prefs: SharedPreferences = PreferenceManager.getDefaultSharedPreferences(requireContext())

        // Načtení a nastavení počáteční aktivity a data (zůstává stejné)
        val savedInitialActivity = prefs.getString(PREF_INITIAL_ACTIVITY, "")
        binding.inputInitialActivity.setText(savedInitialActivity)

        val savedInitialDateMillis = prefs.getLong(PREF_INITIAL_DATE_MILLIS, -1L)
        if (savedInitialDateMillis != -1L) {
            calendar.timeInMillis = savedInitialDateMillis
            updateDateLabelInInputInitialDate(calendar)
        } else {
            binding.inputInitialDate.setText("")
        }

        // NAČTENÍ NORMY Z PREFERENCES (NASTAVENO V SETTINGSFRAGMENT)
        val selectedStandardFromPrefs = prefs.getString("standard", "ASTM") ?: "ASTM"

        // Načtení uložených hodnot pro input pole
        loadSavedInputValues(prefs)

        // Filmy v pořadí jak jsou v JSON (bez řazení)
        val films = dataRepo.films.mapNotNull { it.nazev?.trim() }.distinct()
        val savedFilmType = prefs.getString(PREF_FILM_TYPE, films.firstOrNull())
        setupSpinner(binding.spinnerFilmType, films, savedFilmType) { selectedFilm ->
            prefs.edit().putString(PREF_FILM_TYPE, selectedFilm).apply()
            calculateAndDisplayExposure()
        }

        // Lokalizace pro spinnerObjectType
        val objectTypeKeys = dataRepo.objectTypes.mapNotNull { it.nazev?.trim() }.distinct()
        val localizedObjectTypes = objectTypeKeys.map { key ->
            // Vytvoření názvu resource: např. "Plný materiál" -> "object_type_pln_materi_l" (zjednodušené)
            // Je důležité, aby názvy resource v strings.xml odpovídaly tomuto formátu
            val resourceName = "object_type_" + key.lowercase(Locale.ROOT)
                .replace(" ", "_")
                .replace("-", "_")
                .replace("ě", "e").replace("š", "s").replace("č", "c")
                .replace("ř", "r").replace("ž", "z").replace("ý", "y")
                .replace("á", "a").replace("í", "i").replace("é", "e")
                .replace("ú", "u").replace("ů", "u") // Diakritika
            getStringResourceByName(resourceName, key) // Použije klíč jako fallback
        }
        // Načtení uloženého KLÍČE, ne lokalizovaného názvu
        val currentSelectedObjectTypeKey = prefs.getString("selected_object_type_key", objectTypeKeys.firstOrNull())
        // Najít index uloženého klíče v seznamu původních klíčů pro nastavení spinneru
        val currentLocalizedSelection = if (currentSelectedObjectTypeKey != null) {
            val resourceName = "object_type_" + currentSelectedObjectTypeKey.lowercase(Locale.ROOT)
                .replace(" ", "_")
                .replace("-", "_")
                .replace("ě", "e").replace("š", "s").replace("č", "c")
                .replace("ř", "r").replace("ž", "z").replace("ý", "y")
                .replace("á", "a").replace("í", "i").replace("é", "e")
                .replace("ú", "u").replace("ů", "u")
            getStringResourceByName(resourceName, currentSelectedObjectTypeKey)
        } else {
            localizedObjectTypes.firstOrNull()
        }

        setupSpinner(binding.spinnerObjectType, localizedObjectTypes, currentLocalizedSelection) { selectedLocalizedObjectType ->
            // Najít původní klíč podle lokalizovaného názvu
            val selectedKey = objectTypeKeys.find { key ->
                val resourceName = "object_type_" + key.lowercase(Locale.ROOT)
                    .replace(" ", "_")
                    .replace("-", "_")
                    .replace("ě", "e").replace("š", "s").replace("č", "c")
                    .replace("ř", "r").replace("ž", "z").replace("ý", "y")
                    .replace("á", "a").replace("í", "i").replace("é", "e")
                    .replace("ú", "u").replace("ů", "u")
                getStringResourceByName(resourceName, key) == selectedLocalizedObjectType
            }
            prefs.edit().putString("selected_object_type_key", selectedKey).apply()
            updateObjectTypeIcon(selectedKey ?: "")
            calculateAndDisplayExposure()
        }

        // Nastavit počáteční ikonu
        val initialKey = prefs.getString("selected_object_type_key", "")
        updateObjectTypeIcon(initialKey ?: "")

        val densities = listOf("1.0", "1.5", "2.0", "2.5", "3.0", "3.5", "4.0") // Doplněny hodnoty 3.5 a 4.0
        val savedDensity = prefs.getString(PREF_DENSITY, densities.firstOrNull())
        setupSpinner(binding.spinnerDensity, densities, savedDensity) { selectedDensity ->
            prefs.edit().putString(PREF_DENSITY, selectedDensity).apply()
            calculateAndDisplayExposure()
        }


        // Povolení editace pro inputActivity
        enableActivityInputEditing()

        // Listener pro inputInitialActivity (počáteční aktivity)
        binding.inputInitialActivity.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                prefs.edit().putString(PREF_INITIAL_ACTIVITY, s.toString()).apply()
                isCurrentActivityManuallySet = false // Změna počátetní aktivity, resetovat manuální nastavení
                calculateAndDisplayCurrentActivity()
                // calculateAndDisplayExposure() // Odstraněno, bude voláno z updateActivityField
            }
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        })

        // Listener pro inputActivity (aktu��lní, potenciálně ručně zadaná aktivita)
        binding.inputActivity.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                if (!isUpdatingActivityProgrammatically) {
                    isCurrentActivityManuallySet = true
                    calculateAndDisplayExposure() // Přidáno pro aktualizaci při manuální změně aktivity
                }
            }
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        })

        // Nastavení OnClickListeneru pro inputInitialDate (počáteční datum)
        binding.inputInitialDate.setOnClickListener {
            showDatePickerDialog() // V showDatePickerDialog se po výběru data nastaví isCurrentActivityManuallySet = false
        }

        // Výpočet expozice (zůstává stejný, bere hodnotu z inputActivity)
        binding.textExposureResult.setOnClickListener {
            calculateAndDisplayExposure()
        }

        // Listeners pro EditText pole ovlivňující výpočet expozice s ukládáním
        setupInputFieldWithMemory(binding.inputThickness, PREF_THICKNESS, prefs)
        setupInputFieldWithMemory(binding.inputWeldReinforcement, PREF_WELD_REINFORCEMENT, prefs)
        setupInputFieldWithMemory(binding.inputDistance, PREF_DISTANCE, prefs)
        setupInputFieldWithMemory(binding.inputUserFactor, PREF_USER_FACTOR, prefs)

        // Nastavení spinneru pro HVL typ v ExposureCalculationFragment
        setupHvlTypeSpinner(prefs)

        // Barricade sekce - výpočet vzdálenosti bariéry s pamětí
        setupInputFieldWithMemory(binding.inputNumExposures, PREF_NUM_EXPOSURES, prefs) {
            calculateBarricadeWithHistory()
        }
        setupInputFieldWithMemory(binding.inputBarricadeDose, PREF_BARRICADE_DOSE, prefs) {
            calculateBarricadeWithHistory()
        }
        // Odebrán listener pro inputHvlCollimator, protože byl odstraněn

        // Prvotní naplnění závislých spinnerů na základě normy z SharedPreferences
        updateIsotopeSpinner(selectedStandardFromPrefs)

        // Následující volání zajistí, že se UI aktualizuje na základě výchozích/uložených hodnot
        // calculateAndDisplayCurrentActivity() // Voláno uvnitř updateIsotopeSpinner -> updateMaterialSpinner
        // updateMuValue() // Voláno uvnitř updateMaterialSpinner

        // OPRAVA: Explicitně volat updateMuValue() a calculateAndDisplayCurrentActivity() pro jistotu
        updateMuValue()
        calculateAndDisplayCurrentActivity() // PŘIDÁNO: Načíst aktivitu při spuštění
        calculateAndDisplayExposure() // Konečný výpočet po všem nastavení

        // Implementace stopek
        setupStopwatchButtons()

        // Zobrazení aktuálního systému jednotek
        updateUnitSystemDisplay()

        // Nastavení rozbalovací karty
        setupExpandableCard()
    }

    private fun getStringResourceByName(name: String, defaultValue: String): String {
        val resId = resources.getIdentifier(name, "string", requireContext().packageName)
        return if (resId != 0) {
            getString(resId)
        } else {
            defaultValue // Vrátí původní hodnotu z JSON, pokud překlad neexistuje
        }
    }

    private fun updateObjectTypeIcon(objectTypeKey: String) {
        val iconResource = when (objectTypeKey) {
            "Weld on sheet metal" -> R.drawable.ic_weld_sheet
            "Weld on a pipe" -> R.drawable.ic_weld_pipe
            "Solid round bar" -> R.drawable.ic_solid_material
            "Angled exposure" -> R.drawable.ic_angled_exposure
            else -> R.drawable.ic_weld_sheet // Výchozí ikona
        }
        binding.imageObjectTypeIcon.setImageResource(iconResource)
    }

    private fun formatSecondsToHHMMSS(totalSeconds: Int): String {
        val hours = totalSeconds / 3600
        val minutes = (totalSeconds % 3600) / 60
        val seconds = totalSeconds % 60
        return String.format(Locale.US, "%02d:%02d:%02d", hours, minutes, seconds)
    }

    private fun parseTimeFromHHMMSS(timeString: String): Double? {
        return try {
            val parts = timeString.trim().split(":")
            if (parts.size == 3) {
                val hours = parts[0].toInt()
                val minutes = parts[1].toInt()
                val seconds = parts[2].toInt()
                (hours * 3600 + minutes * 60 + seconds).toDouble()
            } else {
                // Fallback pro starý formát (jen sekundy)
                timeString.trim().lowercase(Locale.US)
                    .replace(" sec", "")
                    .replace(" min", "")
                    .toDoubleOrNull()
            }
        } catch (e: Exception) {
            null
        }
    }

    private fun setStopwatchTime(totalSeconds: Int) {
        val formattedTime = formatSecondsToHHMMSS(totalSeconds)
        binding.textStopwatch.text = formattedTime
        remainingTimeSeconds = totalSeconds

        // Uložit původní čas pro možnost resetu
        val prefs = PreferenceManager.getDefaultSharedPreferences(requireContext())
        prefs.edit().putInt("exposure_time_seconds", totalSeconds).apply()
    }

    private fun setupStopwatchButtons() {
        // Start/Stop tlačítko
        binding.btnStartStopwatch.setOnClickListener {
            if (!isTimerRunning) {
                startCountdown()
            } else {
                stopCountdown()
            }
        }

        // Pause tlačítko
        binding.btnPauseStopwatch.setOnClickListener {
            if (isTimerRunning) {
                pauseCountdown()
            } else if (remainingTimeSeconds > 0) {
                resumeCountdown()
            }
        }

        // Reset tlačítko
        binding.btnResetStopwatch.setOnClickListener {
            resetCountdown()
        }
    }

    private fun startCountdown() {
        if (remainingTimeSeconds <= 0) {
            // Pokud není nastaven čas, použij čas z výpočtu expozice
            val prefs = PreferenceManager.getDefaultSharedPreferences(requireContext())
            remainingTimeSeconds = prefs.getInt("exposure_time_seconds", 0)
        }

        if (remainingTimeSeconds > 0) {
            isTimerRunning = true
            binding.btnStartStopwatch.text = getString(R.string.stop)
            binding.btnPauseStopwatch.isEnabled = true

            countdownTimer = object : CountDownTimer((remainingTimeSeconds * 1000).toLong(), 1000) {
                override fun onTick(millisUntilFinished: Long) {
                    remainingTimeSeconds = (millisUntilFinished / 1000).toInt()
                    binding.textStopwatch.text = formatSecondsToHHMMSS(remainingTimeSeconds)
                }

                override fun onFinish() {
                    onCountdownFinished()
                }
            }.start()
        }
    }

    private fun stopCountdown() {
        countdownTimer?.cancel()
        isTimerRunning = false
        binding.btnStartStopwatch.text = getString(R.string.start)
        binding.btnPauseStopwatch.isEnabled = false

        // Reset na původní čas
        resetCountdown()
    }

    private fun pauseCountdown() {
        countdownTimer?.cancel()
        isTimerRunning = false
        binding.btnStartStopwatch.text = getString(R.string.start)
        binding.btnPauseStopwatch.isEnabled = false
    }

    private fun resumeCountdown() {
        startCountdown()
    }

    private fun resetCountdown() {
        countdownTimer?.cancel()
        isTimerRunning = false
        binding.btnStartStopwatch.text = getString(R.string.start)
        binding.btnPauseStopwatch.isEnabled = false

        // Obnovit původní čas z výpočtu expozice
        val prefs = PreferenceManager.getDefaultSharedPreferences(requireContext())
        val originalTime = prefs.getInt("exposure_time_seconds", 0)
        remainingTimeSeconds = originalTime
        binding.textStopwatch.text = formatSecondsToHHMMSS(originalTime)
    }

    private fun onCountdownFinished() {
        isTimerRunning = false
        remainingTimeSeconds = 0
        binding.textStopwatch.text = "00:00:00"
        binding.btnStartStopwatch.text = getString(R.string.start)
        binding.btnPauseStopwatch.isEnabled = false

        // Zde můžete přidat alarm nebo notifikaci
        // playAlarm() // Pokud máte implementovaný alarm
    }

    // **OPRAVENÁ METODA setupSpinner**
    private fun setupSpinner(spinner: android.widget.Spinner, items: List<String>, selectedItem: String?, onItemSelected: (selectedString: String) -> Unit) {
        if (items.isEmpty()) {
            spinner.adapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, listOf(getString(R.string.no_data_available)))
            spinner.isEnabled = false
            return
        }
        spinner.isEnabled = true
        val adapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, items)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        spinner.adapter = adapter

        // Korektní a jasná logika pro nastavení počáteční vybrané položky
        var initialPositionToSelect = 0 // Výchozí pozice je první položka
        if (selectedItem != null) {
            val indexOfSelectedItem = items.indexOf(selectedItem)
            if (indexOfSelectedItem >= 0) {
                initialPositionToSelect = indexOfSelectedItem // Položka nalezena, vyber ji
            }
            // Pokud selectedItem není null, ale není nalezen v items, initialPositionToSelect zůstane 0 (vybere se první položka)
        }
        // Pokud selectedItem je null, initialPositionToSelect zůstane 0 (vybere se první položka)

        // Toto je bezpečné, protože jsme na začátku zkontrolovali, zda items.isEmpty()
        if (items.isNotEmpty()) { // Dodatečná kontrola pro jistotu před setSelection
            spinner.setSelection(initialPositionToSelect)
        }


        spinner.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                if (position >= 0 && position < items.size) { // Kontrola hranic
                    onItemSelected(items[position])
                }
            }
            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }
    }

    private fun showDatePickerDialog() {
        val year = calendar.get(Calendar.YEAR)
        val month = calendar.get(Calendar.MONTH)
        val day = calendar.get(Calendar.DAY_OF_MONTH)

        val datePickerDialog = DatePickerDialog(
            requireContext(),
            { _, selectedYear, selectedMonth, selectedDay ->
                calendar.set(selectedYear, selectedMonth, selectedDay)
                updateDateLabelInInputInitialDate(calendar)

                val localPrefs: SharedPreferences = PreferenceManager.getDefaultSharedPreferences(requireContext())
                localPrefs.edit().putLong(PREF_INITIAL_DATE_MILLIS, calendar.timeInMillis).apply()

                isCurrentActivityManuallySet = false // Změna data, resetovat manuální nastavení
                calculateAndDisplayCurrentActivity()
                // OPRAVA: Přidat explicitní volání výpočtu expozice po změně data
                calculateAndDisplayExposure()
            },
            year,
            month,
            day
        )
        datePickerDialog.show()
    }

    private fun updateDateLabelInInputInitialDate(cal: Calendar) {
        val myFormat = "dd.MM.yyyy" // Preferovaný formát data
        val sdf = SimpleDateFormat(myFormat, Locale.getDefault())
        binding.inputInitialDate.setText(sdf.format(cal.time))
    }

    private fun updateIsotopeSpinner(selectedStandard: String?) {
        val prefs = PreferenceManager.getDefaultSharedPreferences(requireContext())
        if (selectedStandard == null) {
            setupSpinner(binding.spinnerIsotope, emptyList(), null) { _ -> }
            updateMaterialSpinner(null, null) // Vyčistit i materiály
            return
        }

        val isotopesForStandard = dataRepo.materials
            .filter { it.norma?.trim() == selectedStandard }
            .mapNotNull { it.izotop?.trim() }
            .distinct()

        val lastSelectedIsotopeForStandard = prefs.getString("last_isotope_for_$selectedStandard", isotopesForStandard.firstOrNull())

        setupSpinner(binding.spinnerIsotope, isotopesForStandard, lastSelectedIsotopeForStandard) { selectedIsotope ->
            prefs.edit().putString("last_isotope_for_$selectedStandard", selectedIsotope).apply()
            isCurrentActivityManuallySet = false
            calculateAndDisplayCurrentActivity()
            updateMaterialSpinner(selectedStandard, selectedIsotope)
            // OPRAVA: Přidat explicitní volání výpočtu expozice
            calculateAndDisplayExposure()
        }

        if (isotopesForStandard.isEmpty()) {
            updateMaterialSpinner(selectedStandard, null)
        } else if (lastSelectedIsotopeForStandard != null && isotopesForStandard.contains(lastSelectedIsotopeForStandard)) {
            updateMaterialSpinner(selectedStandard, lastSelectedIsotopeForStandard)
        } else if (isotopesForStandard.isNotEmpty()) {
            updateMaterialSpinner(selectedStandard, isotopesForStandard.first())
        } else {
            updateMaterialSpinner(selectedStandard, null)
        }
    }

    private fun updateMaterialSpinner(selectedStandard: String?, selectedIsotope: String?) {
        val prefs = PreferenceManager.getDefaultSharedPreferences(requireContext())
        if (selectedStandard == null || selectedIsotope == null) {
            setupSpinner(binding.spinnerMaterial, emptyList(), null) { _ -> }
            updateMuValue()
            return
        }

        val materialsForStandardAndIsotope = dataRepo.materials
            .filter { it.norma?.trim() == selectedStandard && it.izotop?.trim() == selectedIsotope }
            .mapNotNull { it.material?.trim() }
            .distinct()

        val lastSelectedMaterial = prefs.getString("last_material_for_${selectedStandard}_$selectedIsotope", materialsForStandardAndIsotope.firstOrNull())

        setupSpinner(binding.spinnerMaterial, materialsForStandardAndIsotope, lastSelectedMaterial) { selectedMaterial ->
            prefs.edit().putString("last_material_for_${selectedStandard}_$selectedIsotope", selectedMaterial).apply()
            updateMuValue()
            // OPRAVA: Přidat explicitní volání výpočtu expozice
            calculateAndDisplayExposure()
        }

        if (materialsForStandardAndIsotope.isEmpty()) {
            updateMuValue()
        } else if (lastSelectedMaterial != null && materialsForStandardAndIsotope.contains(lastSelectedMaterial)) {
            updateMuValue()
        } else if (materialsForStandardAndIsotope.isNotEmpty()){
            updateMuValue()
        } else {
            updateMuValue()
        }
    }

    private fun updateMuValue() {
        val prefs = PreferenceManager.getDefaultSharedPreferences(requireContext())
        val selectedStandard = prefs.getString("standard", "ASTM") ?: "ASTM" // Na��tení normy z SharedPreferences
        val selectedIsotope = binding.spinnerIsotope.selectedItem?.toString()
        val selectedMaterial = binding.spinnerMaterial.selectedItem?.toString()

        android.util.Log.d("ExposureCalc", "updateMuValue() - Standard: $selectedStandard, Isotope: $selectedIsotope, Material: $selectedMaterial")

        if (selectedStandard == getString(R.string.no_data_available) || // Přidána kontrola i pro selectedStandard, i když by měl být vždy dostupný z prefs
            selectedIsotope == null || selectedIsotope == getString(R.string.no_data_available) ||
            selectedMaterial == null || selectedMaterial == getString(R.string.no_data_available)) {
            // Nastavit μ hodnotu na null místo zobrazování "--"
            currentMuValue = null
            android.util.Log.d("ExposureCalc", "μ hodnota resetována na null (chybí data)")
            calculateAndDisplayExposure()
            return
        }

        val materialData = dataRepo.materials.firstOrNull {
            it.norma?.trim() == selectedStandard && it.izotop?.trim() == selectedIsotope && it.material?.trim() == selectedMaterial
        }

        android.util.Log.d("ExposureCalc", "materialData found: ${materialData != null}")
        if (materialData != null) {
            android.util.Log.d("ExposureCalc", "materialData.mu: '${materialData.mu}'")
        }

        val muValueString = materialData?.mu?.trim()?.replace(",", ".")
        val muValue = muValueString?.toDoubleOrNull()

        android.util.Log.d("ExposureCalc", "muValueString: '$muValueString', muValue: $muValue")

        // Uložit μ hodnotu do proměnné místo zobrazování v UI
        currentMuValue = muValue
        android.util.Log.d("ExposureCalc", "μ hodnota nastavena: $currentMuValue")

        // OPRAVA: Vždy volat výpočet expozice po aktualizaci μ hodnoty
        calculateAndDisplayExposure()
    }

    private fun calculateAndDisplayCurrentActivity() {
        // Pokud je aktivita nastavena manuálně a tato metoda NENENÍ volána kvůli změně vstupních parametrů,
        // pak bychom neměli přepočítávat. Toto je nyní řešeno resetem isCurrentActivityManuallySet
        // před voláním této metody z míst, kde se mění vstupní parametry.

        val initialActivityStr = binding.inputInitialActivity.text.toString()
        val initialActivity = initialActivityStr.toDoubleOrNull()
        val initialDateSet = binding.inputInitialDate.text.isNotBlank()

        if (initialActivity == null || !initialDateSet) {
            updateActivityField("")
            return
        }

        val selectedIsotopeName = binding.spinnerIsotope.selectedItem?.toString()
        if (selectedIsotopeName == null || selectedIsotopeName == getString(R.string.no_data_available)) {
            updateActivityField("")
            return
        }

        val isotopeData = dataRepo.isotopes.firstOrNull { it.nazev == selectedIsotopeName }
        val halfLifeStr = isotopeData?.polocas
        val halfLifeDays = halfLifeStr?.replace(",",".")?.toDoubleOrNull()

        if (halfLifeDays == null || halfLifeDays <= 0) {
            updateActivityField(getString(R.string.error_half_life_missing))
            return
        }

        val initialDateMillis = calendar.timeInMillis
        val currentDateMillis = System.currentTimeMillis()

        if (initialDateMillis != 0L && currentDateMillis < initialDateMillis) {
            updateActivityField(getString(R.string.error_initial_date_in_future))
            return
        }

        val elapsedTimeMillis = currentDateMillis - initialDateMillis
        val elapsedTimeDays = elapsedTimeMillis / (1000.0 * 60 * 60 * 24)
        val lambda = ln(2.0) / halfLifeDays
        val currentActivityValue = initialActivity * exp(-lambda * elapsedTimeDays)

        updateActivityField(String.format(Locale.US, "%.2f", currentActivityValue))
    }

    // Pomocná metoda pro aktualizaci pole inputActivity a správu příznaku
    private fun updateActivityField(text: String) {
        isUpdatingActivityProgrammatically = true
        binding.inputActivity.setText(text)
        binding.inputActivity.setSelection(binding.inputActivity.text.length) // Posune kurzor na konec
        isUpdatingActivityProgrammatically = false
        // Volání calculateAndDisplayExposure po aktualizaci pole aktivity, pokud je text platný
        if (text.isNotBlank() && !text.startsWith(getString(R.string.error_prefix))) { // Předpokládáme, že chybové hláška začínají prefixem "Error"
             calculateAndDisplayExposure()
        } else if (text.isBlank()) {
            binding.textExposureResult.text = "--" // Vyčistit výsledek, pokud je aktivita prázdná
        }
    }

    // Metoda pro výpočet a zobrazení času expozice
    private fun calculateAndDisplayExposure() {
        // DEBUG: Log pro sledování volání
        android.util.Log.d("ExposureCalc", "calculateAndDisplayExposure() called")
        android.util.Log.d("ExposureCalc", "Device Locale: ${Locale.getDefault()}")

        val prefs = PreferenceManager.getDefaultSharedPreferences(requireContext())
        val unitSystem = prefs.getString("unit_system", "Metrický") ?: "Metrický"
        // val currentStandard = prefs.getString("standard", "ASTM") ?: "ASTM" // Již se nepoužívá přímo zde pro K-Gamma
        val selectedActivityUnit = prefs.getString("activity_unit", "GBq") ?: "GBq"

        val activityInputString = binding.inputActivity.text.toString()
        val activityInputVal = activityInputString.toDoubleOrNull()

        android.util.Log.d("ExposureCalc", "activityInputString: '$activityInputString'")
        android.util.Log.d("ExposureCalc", "activityInputVal: $activityInputVal")

        if (activityInputVal == null || activityInputVal <= 0) {
            android.util.Log.d("ExposureCalc", "CHYBA: Neplatná aktivita")
            if (activityInputString.isNotBlank() && !activityInputString.startsWith(getString(R.string.error_prefix))) {
                 binding.textExposureResult.text = getString(R.string.error_activity_invalid)
            } else if (activityInputString.isBlank()){
                 binding.textExposureResult.text = "--"
            }
            return
        }

        // Konverze aktivity na Curie (Ci) pro výpočet
        val activityInCi: Double = if (selectedActivityUnit == "GBq") {
            activityInputVal / GBQ_PER_CI
        } else { // Předpokládáme, že druhá možnost je "Ci"
            activityInputVal
        }

        val izotop = binding.spinnerIsotope.selectedItem?.toString()
        val material = binding.spinnerMaterial.selectedItem?.toString()
        val film = binding.spinnerFilmType.selectedItem?.toString()

        if (izotop == null || izotop == getString(R.string.no_data_available) ||
            material == null || material == getString(R.string.no_data_available) ||
            film == null || film == getString(R.string.no_data_available)) {
            binding.textExposureResult.text = "--"
            return
        }

        // Načtení KLÍČE typu objektu z SharedPreferences
        val objectTypeKey = prefs.getString("selected_object_type_key", "") ?: ""
        val density = binding.spinnerDensity.selectedItem?.toString()?.toDoubleOrNull() ?: 1.0
        val userFactor = binding.inputUserFactor.text.toString().replace(',', '.').toDoubleOrNull() ?: 0.0

        val thicknessInputVal = binding.inputThickness.text.toString().replace(',', '.').toDoubleOrNull() ?: 0.0
        val weldReinforcementInputVal = binding.inputWeldReinforcement.text.toString().replace(',', '.').toDoubleOrNull() ?: 0.0
        val distanceInputVal = binding.inputDistance.text.toString().replace(',', '.').toDoubleOrNull() ?: 1.0

        android.util.Log.d("ExposureCalc", "thicknessInputVal: $thicknessInputVal")
        android.util.Log.d("ExposureCalc", "weldReinforcementInputVal: $weldReinforcementInputVal")
        android.util.Log.d("ExposureCalc", "distanceInputVal: $distanceInputVal")
        android.util.Log.d("ExposureCalc", "unitSystem: $unitSystem")

        // Převod tloušťky a převýšení na mm (pro mu_mm)
        val thicknessMm: Double
        val weldReinforcementMm: Double
        if (unitSystem == "Americký" || unitSystem == "Britský") { // Vstup v palcích
            thicknessMm = thicknessInputVal * MM_PER_INCH
            weldReinforcementMm = weldReinforcementInputVal * MM_PER_INCH
        } else { // Metrický, vstup v mm
            thicknessMm = thicknessInputVal
            weldReinforcementMm = weldReinforcementInputVal
        }

        // OCHRANA: Kontrola rozumných hodnot tloušťky
        if (thicknessMm > 1000) { // Více než 1 metr
            binding.textExposureResult.text = "CHYBA: Tloušťka příliš velká (${thicknessMm.toInt()} mm). Zkontrolujte jednotky."
            return
        }
        val effectiveThicknessMm = when (objectTypeKey) { // Porovnání s KLÍČEM z JSON
            "Weld on sheet metal" -> thicknessMm + weldReinforcementMm
            "Weld on a pipe" -> thicknessMm + (2.0 * weldReinforcementMm)  // 2× převýšení pro kolmé prozařování
            "Solid round bar" -> (thicknessMm + weldReinforcementMm) / 2.0 // Poloviční expozice pro válcový objekt
            "Angled exposure" -> {
                // Šikmá expozice ~70° od kolmice (20° od svislice) → úhlový faktor ≈ 1.064 (1/cos(20°))
                val prefs = PreferenceManager.getDefaultSharedPreferences(requireContext())
                val angleCorrectionEnabled = prefs.getBoolean("angle_correction_enabled", true)

                val angleThickness = if (angleCorrectionEnabled) {
                    thicknessMm * 1.064  // S úhlovou korekcí
                } else {
                    thicknessMm  // Bez úhlové korekce
                }
                angleThickness + weldReinforcementMm  // 1× převýšení (nejsou nad sebou)
            }
            else -> thicknessMm + weldReinforcementMm // Výchozí logika
        }

        // DEBUG: Kontrola unit system
        android.util.Log.d("ExposureCalc", "unitSystem: '$unitSystem'")
        android.util.Log.d("ExposureCalc", "distanceInputVal: $distanceInputVal")

        // Převod vzdálenosti na stopy (feet) pro K-gamma konstanty
        val distanceForK_InFeet: Double = if (unitSystem == "Metrický") { // Vstup v mm
            distanceInputVal / MM_PER_FOOT
        } else { // Vstup ve stopách (Americký/Britský)
            distanceInputVal
        }

        // OCHRANA: Kontrola rozumných hodnot vzdálenosti
        if (distanceForK_InFeet > 100) { // Více než 100 stop (30 metrů)
            binding.textExposureResult.text = "CHYBA: Vzdálenost příliš velká (${distanceForK_InFeet.toInt()} ft). Zkontrolujte jednotky."
            return
        }
        if (distanceForK_InFeet < 0.3) { // Méně než 30 cm
            binding.textExposureResult.text = "CHYBA: Vzdálenost příliš malá (${String.format("%.1f", distanceForK_InFeet)} ft). Zkontrolujte jednotky."
            return
        }

        android.util.Log.d("ExposureCalc", "distanceForK_InFeet: $distanceForK_InFeet")

        // Použít μ hodnotu z proměnné místo čtení z UI
        val mu = currentMuValue
        android.util.Log.d("ExposureCalc", "currentMuValue: $currentMuValue")
        if (mu == null) {
            android.util.Log.e("ExposureCalc", "CHYBA: μ hodnota je null!")
            binding.textExposureResult.text = getString(R.string.error_mu_value_not_found)
            return
        }

        val rFactor: Double
        val selectedFilmObject = dataRepo.films.firstOrNull { it.nazev == film }

        if (selectedFilmObject == null) {
            binding.textExposureResult.text = getString(R.string.error_film_data_missing) // Přidejte tento string resource
            return
        }

        // Výběr správného R-faktoru na základě spinnerDensity
        // OPRAVA: Zjednodušené mapování s novými klíči (density × 10)
        val densityKey = "r_faktor_" + binding.spinnerDensity.selectedItem?.toString()?.replace(".", "")

        rFactor = when (densityKey) {
            "r_faktor_10" -> selectedFilmObject.r_faktor_10?.toString()?.replace(',', '.')?.toDoubleOrNull()
            "r_faktor_15" -> selectedFilmObject.r_faktor_15?.toString()?.replace(',', '.')?.toDoubleOrNull()
            "r_faktor_20" -> selectedFilmObject.r_faktor_20?.toString()?.replace(',', '.')?.toDoubleOrNull()
            "r_faktor_25" -> selectedFilmObject.r_faktor_25?.toString()?.replace(',', '.')?.toDoubleOrNull()
            "r_faktor_30" -> selectedFilmObject.r_faktor_30?.toString()?.replace(',', '.')?.toDoubleOrNull()
            "r_faktor_35" -> selectedFilmObject.r_faktor_35?.toString()?.replace(',', '.')?.toDoubleOrNull()
            "r_faktor_40" -> selectedFilmObject.r_faktor_40?.toString()?.replace(',', '.')?.toDoubleOrNull()
            else -> 1.0 // Výchozí hodnota, pokud klíč neodpovídá
        } ?: 1.0 // Výchozí hodnota, pokud je r_faktor null

        val isotopeRow = dataRepo.isotopes.firstOrNull { it.nazev == izotop }
        // Rozhodnutí, zda použít emisivitu nebo K-Gamma
        val kGammaOrEmissivityValue: Double?
        val useEmissivity = izotop == "Ir192" && prefs.contains("current_emissivity_value")

        if (useEmissivity) {
            val emissivityString = prefs.getString("current_emissivity_value", null)
            kGammaOrEmissivityValue = if (emissivityString.isNullOrBlank()) null else emissivityString.replace(',', '.').toDoubleOrNull()
        } else {
            val kGammaString = prefs.getString("current_k_gamma_value", null)
            kGammaOrEmissivityValue = if (kGammaString.isNullOrBlank()) null else kGammaString.replace(',', '.').toDoubleOrNull()
        }

        if (kGammaOrEmissivityValue == null) {
            binding.textExposureResult.text = getString(R.string.error_exposure_const_missing)
            return
        }

        // DEBUG: Kontrola hodnot před výpočtem
        if (activityInCi < 0.001) {
            binding.textExposureResult.text = "CHYBA: Aktivita příliš malá ($activityInCi Ci)"
            return
        }
        if (distanceForK_InFeet < 0.1) {
            binding.textExposureResult.text = "CHYBA: Vzdálenost příliš malá ($distanceForK_InFeet ft)"
            return
        }
        if (mu > 10.0) {
            binding.textExposureResult.text = "CHYBA: μ příliš velké ($mu)"
            return
        }
        if (kGammaOrEmissivityValue < 0.1) {
            binding.textExposureResult.text = "CHYBA: K-gamma příliš malé ($kGammaOrEmissivityValue)"
            return
        }

        // Výpočet: T_hours = (d_ft^2 * e^(µx_mm) * (rFactor + userFactor)) / (A_Ci * K_gamma_nebo_Emisivita)
        // OPRAVA: Density se používá pouze pro výběr rFactoru, ne jako multiplikátor ve vzorci
        // R-faktor již obsahuje informaci o požadovaném zčernání (density)

        // OCHRANA: Kontrola exponenciální funkce před výpočtem
        val muTimesThickness = mu * effectiveThicknessMm
        if (muTimesThickness > 23) { // exp(23) ≈ 10^10
            binding.textExposureResult.text = "CHYBA: Materiál příliš tlustý pro radiografii (μ×x=${String.format("%.1f", muTimesThickness)}). Zkontrolujte tloušťku a jednotky."
            return
        }

        val expValue = exp(muTimesThickness)
        if (expValue > 1e10) {
            binding.textExposureResult.text = "CHYBA: Exponenciální funkce příliš velká. Zkontrolujte nastavení jednotek."
            return
        }

        val numerator = distanceForK_InFeet.pow(2.0) * expValue * (rFactor + userFactor)
        val denominator = activityInCi * kGammaOrEmissivityValue

        // DEBUG: Podrobný log všech hodnot
        android.util.Log.d("ExposureCalc", "=== VÝPOČET EXPOZICE ===")
        android.util.Log.d("ExposureCalc", "distanceForK_InFeet: $distanceForK_InFeet")
        android.util.Log.d("ExposureCalc", "mu: $mu")
        android.util.Log.d("ExposureCalc", "effectiveThicknessMm: $effectiveThicknessMm")
        android.util.Log.d("ExposureCalc", "rFactor: $rFactor")
        android.util.Log.d("ExposureCalc", "userFactor: $userFactor")
        android.util.Log.d("ExposureCalc", "activityInCi: $activityInCi")
        android.util.Log.d("ExposureCalc", "kGammaOrEmissivityValue: $kGammaOrEmissivityValue")
        android.util.Log.d("ExposureCalc", "exp(mu * effectiveThicknessMm): ${exp(mu * effectiveThicknessMm)}")
        android.util.Log.d("ExposureCalc", "numerator: $numerator")
        android.util.Log.d("ExposureCalc", "denominator: $denominator")

        if (denominator > 0 && activityInCi > 0) { // Kontrola activityInCi pro jistotu
            val exposureTimeInHours = numerator / denominator
            val exposureTimeInSeconds = exposureTimeInHours * 3600.0

            android.util.Log.d("ExposureCalc", "exposureTimeInHours: $exposureTimeInHours")
            android.util.Log.d("ExposureCalc", "exposureTimeInSeconds: $exposureTimeInSeconds")

            // OCHRANA: Kontrola rozumných hodnot času expozice
            if (exposureTimeInSeconds > 86400) { // Více než 24 hodin
                binding.textExposureResult.text = "CHYBA: Čas expozice příliš dlouhý (${String.format("%.1f", exposureTimeInSeconds/3600)} hodin). Zkontrolujte parametry."
                return
            }

            if (exposureTimeInSeconds < 1) { // Méně než 1 sekunda
                binding.textExposureResult.text = "CHYBA: Čas expozice příliš krátký (${String.format("%.2f", exposureTimeInSeconds)} s). Zkontrolujte aktivitu."
                return
            }

            if (exposureTimeInSeconds < 0) {
                android.util.Log.e("ExposureCalc", "CHYBA: Záporný čas expozice!")
                binding.textExposureResult.text = "CHYBA: Záporný čas"
                return
            }

            val formattedTime = formatSecondsToHHMMSS(exposureTimeInSeconds.toInt())
            android.util.Log.d("ExposureCalc", "formattedTime: $formattedTime")

            binding.textExposureResult.text = formattedTime

            // Automaticky nastavit čas do stopek
            setStopwatchTime(exposureTimeInSeconds.toInt())
        } else {
            android.util.Log.d("ExposureCalc", "CHYBA: denominator <= 0 nebo activityInCi <= 0")
            binding.textExposureResult.text = "--" // Chyba dělení nulou nebo neplatná aktivita
        }
    }


    // Nahrazuje disableActivityInputEditing()
    private fun enableActivityInputEditing() {
        binding.inputActivity.isFocusable = true
        binding.inputActivity.isFocusableInTouchMode = true
        binding.inputActivity.isClickable = true
    }

    // ... (calculateBarricade, saveBarricadeHistory, calculateBarricadeWithHistory, onDestroyView - beze změn v logice, ale používají hodnotu z inputActivity)

    private fun calculateBarricade() {
        val numExposures = binding.inputNumExposures.text.toString().toIntOrNull() ?: 1
        val barricadeDoseText = binding.inputBarricadeDose.text.toString().replace(",",".")
        val barricadeDose = barricadeDoseText.toDoubleOrNull() ?: 2.0

        val activityText = binding.inputActivity.text.toString() // Již by mělo být s tečkou z updateActivityField
        val activity = activityText.toDoubleOrNull()

        val exposureTimeText = binding.textExposureResult.text.toString().replace(" min", "") // Již by mělo být s tečkou
        val exposureTime = exposureTimeText.toDoubleOrNull()

        val izotop = binding.spinnerIsotope.selectedItem?.toString()

        if (activity == null || activity <= 0) {
            binding.textCalculatedBarricadeDistance.text = getString(R.string.error_activity_invalid)
            return
        }
        if (exposureTime == null || exposureTime <= 0) {
            binding.textCalculatedBarricadeDistance.text = getString(R.string.error_exposure_time_invalid) // Použijte nebo vytvořte tento string resource
            return
        }
        if (izotop == null || izotop == getString(R.string.no_data_available)) {
            binding.textCalculatedBarricadeDistance.text = "--"
            return
        }
        if (barricadeDose <= 0) {
            binding.textCalculatedBarricadeDistance.text = getString(R.string.error_invalid_barricade_inputs)
            return
        }

        val prefs = PreferenceManager.getDefaultSharedPreferences(requireContext())
        // Rozhodnutí, zda použít emisivitu nebo K-Gamma pro výpočet bariéry
        val kGammaOrEmissivityValue: Double?
        val useEmissivity = izotop == "Ir192" && prefs.contains("current_emissivity_value")

        if (useEmissivity) {
            val emissivityString = prefs.getString("current_emissivity_value", null)
            kGammaOrEmissivityValue = if (emissivityString.isNullOrBlank()) null else emissivityString.replace(',', '.').toDoubleOrNull()
        } else {
            val kGammaString = prefs.getString("current_k_gamma_value", null)
            kGammaOrEmissivityValue = if (kGammaString.isNullOrBlank()) null else kGammaString.replace(',', '.').toDoubleOrNull()
        }

        if (kGammaOrEmissivityValue == null) {
            binding.textCalculatedBarricadeDistance.text = getString(R.string.error_exposure_const_missing)
            return
        }

        // Nahrazeno přímým čtením z SharedPreferences podle výběru ve spinnerHvlType
        val hvlValueForCalculation = prefs.getString("current_hvl_value_for_calculation", "0.0")?.replace(",",".")?.toDoubleOrNull() ?: 0.0

        val numerator = activity * kGammaOrEmissivityValue * exposureTime * numExposures.toDouble() // exposureTime je zde v hodinách, pokud se nemýlím z předchozí logiky
        val denominator = barricadeDose * hvlValueForCalculation // hvlCollimator je zde faktor útlumu, ne počet HVL
        val barricadeDistance = if (denominator > 0) kotlin.math.sqrt(numerator / denominator) else 0.0
        // OPRAVA: Zápis do textCalculatedBarricadeDistance namísto inputDistance
        binding.textCalculatedBarricadeDistance.text = String.format(Locale.US, "%.1f m", barricadeDistance)
    }

    private fun saveBarricadeHistory(doseValue: Double, doseUnit: String, exposures: Int, distanceInMeters: Double) {
        val sdf = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
        val dateStr = sdf.format(java.util.Date())
        // Zobrazení původní hodnoty dávky a její jednotky v historii
        val entry = "${dateStr} | Dávka: ${String.format(Locale.getDefault(), "%.2f", doseValue)} $doseUnit | Expozic: $exposures | Vzdálenost: ${String.format(Locale.US, "%.1f", distanceInMeters)} m"

        val prefs: SharedPreferences = PreferenceManager.getDefaultSharedPreferences(requireContext())
        val historyKey = "barricade_history"

        val currentHistorySet: Set<String>? = prefs.getStringSet(historyKey, null)
        val historyList: MutableList<String> = currentHistorySet?.toMutableList() ?: mutableListOf()

        historyList.add(0, entry) // Přidání na začátek seznamu
        if (historyList.size > 50) { // Omezení velikosti historie
            if (historyList.isNotEmpty()) {
                 historyList.removeAt(historyList.lastIndex) // Odstranění nejstaršího záznamu
            }
        }
        prefs.edit().putStringSet(historyKey, historyList.toSet()).apply()

        // Aktualizace ListView
        val listView = binding.root.findViewById<android.widget.ListView?>(R.id.listBarricadeHistory)
        (listView?.adapter as? ArrayAdapter<String>)?.let {
            it.clear()
            it.addAll(historyList)
            it.notifyDataSetChanged()
        } ?: run {
            // Pokud adapter ještě neexistuje, vytvoříme nový
            if (listView != null) { // Ujistíme se, že listView není null
                 listView.adapter = ArrayAdapter(requireContext(), android.R.layout.simple_list_item_1, historyList)
            }
        }
    }

    private fun calculateBarricadeWithHistory() {
        print("BarricadeCalc: Calculating barricade. Exposure result text: '${binding.textExposureResult.text}'")

        val prefs = PreferenceManager.getDefaultSharedPreferences(requireContext())
        val selectedActivityUnit = prefs.getString("activity_unit", "GBq") ?: "GBq"
        val selectedDoseUnit = prefs.getString("dose_unit", "mR") ?: "mR"

        val numExposures = binding.inputNumExposures.text.toString().toIntOrNull() ?: 1
        val barricadeDoseInput = binding.inputBarricadeDose.text.toString().replace(",", ".").toDoubleOrNull() ?: 2.0

        // Použije hodnotu z SharedPreferences, která je určena spinnerHvlType
        val hvlValueForCalculation = prefs.getString("current_hvl_value_for_calculation", "0.0")?.replace(",",".")?.toDoubleOrNull() ?: 0.0

        val activityText = binding.inputActivity.text.toString()
        val activityInputVal = activityText.toDoubleOrNull()

        val exposureTimeResultText = binding.textExposureResult.text.toString()
        val exposureTimeInSeconds = parseTimeFromHHMMSS(exposureTimeResultText)

        val izotop = binding.spinnerIsotope.selectedItem?.toString()

        if (activityInputVal == null || activityInputVal <= 0) {
            binding.textCalculatedBarricadeDistance.text = getString(R.string.error_activity_invalid)
            return
        }
        if (exposureTimeInSeconds == null || exposureTimeInSeconds <= 0) {
            binding.textCalculatedBarricadeDistance.text = getString(R.string.error_exposure_time_invalid) + " (Obsah: '$exposureTimeResultText')"
            return
        }
        if (izotop == null || izotop == getString(R.string.no_data_available)) {
            binding.textCalculatedBarricadeDistance.text = getString(R.string.error_isotope_not_selected)
            return
        }
        if (barricadeDoseInput <= 0) {
            binding.textCalculatedBarricadeDistance.text = getString(R.string.error_invalid_barricade_inputs)
            return
        }

        val activityInCi: Double = if (selectedActivityUnit == "GBq") {
            activityInputVal / GBQ_PER_CI
        } else {
            activityInputVal
        }

        val exposureTimeInHours = exposureTimeInSeconds / 3600.0

        val barricadeDoseInR: Double = when (selectedDoseUnit) {
            "mR" -> barricadeDoseInput / 1000.0
            "µSv" -> barricadeDoseInput / 10000.0
            "mSv" -> barricadeDoseInput / 10.0
            else -> barricadeDoseInput / 1000.0
        }

        val kGammaOrEmissivityValue: Double?
        val useEmissivity = izotop == "Ir192" && prefs.contains("current_emissivity_value")

        if (useEmissivity) {
            val emissivityString = prefs.getString("current_emissivity_value", null)
            kGammaOrEmissivityValue = if (emissivityString.isNullOrBlank()) null else emissivityString.replace(',', '.').toDoubleOrNull()
        } else {
            val kGammaString = prefs.getString("current_k_gamma_value", null)
            kGammaOrEmissivityValue = if (kGammaString.isNullOrBlank()) null else kGammaString.replace(',', '.').toDoubleOrNull()
        }

        if (kGammaOrEmissivityValue == null) {
            binding.textCalculatedBarricadeDistance.text = getString(R.string.error_exposure_const_missing)
            return
        }

        // Faktor útlumu stínění S = 2^(N_hvl)
        // N_hvl je hvlValueForCalculation
        val shieldingAttenuation = 2.0.pow(hvlValueForCalculation)

        val numeratorCalc = activityInCi * kGammaOrEmissivityValue * exposureTimeInHours * numExposures.toDouble()
        val denominatorCalc = barricadeDoseInR * shieldingAttenuation

        if (denominatorCalc <= 0) {
            binding.textCalculatedBarricadeDistance.text = getString(R.string.error_calculation) + " (jmenovatel <= 0)"
            return
        }

        val distanceSquared = numeratorCalc / denominatorCalc
        if (distanceSquared < 0) {
             binding.textCalculatedBarricadeDistance.text = getString(R.string.error_calculation_negative_sqrt)
             return
        }

        val calculatedDistancePrimitive = kotlin.math.sqrt(distanceSquared)

        val distanceInMeters: Double
        var constantUnits = "R·ft²/Ci·hr"
        if (useEmissivity) {
            // Předpoklad R·ft²/Ci·hr pro emisivitu
        } else {
            val selectedIsotopeName = binding.spinnerIsotope.selectedItem?.toString()
            val kGammaDisplayForIsotope = prefs.getString("selected_k_gamma_display_for_$selectedIsotopeName", null)
            if (kGammaDisplayForIsotope != null && kGammaDisplayForIsotope.contains("R·m²/Ci·hr", ignoreCase = true)) {
                constantUnits = "R·m²/Ci·hr"
            }
        }

        if (constantUnits == "R·m��/Ci·hr") {
            distanceInMeters = calculatedDistancePrimitive
        } else {
            distanceInMeters = calculatedDistancePrimitive * METERS_PER_FOOT
        }

        if (distanceInMeters.isInfinite() || distanceInMeters.isNaN()) {
            binding.textCalculatedBarricadeDistance.text = getString(R.string.error_calculation) + " (výsledek NaN/Infinite)"
            return
        }

        binding.textCalculatedBarricadeDistance.text = String.format(Locale.US, "%.1f m", distanceInMeters)

        if (activityInCi > 0 && exposureTimeInHours > 0 && kGammaOrEmissivityValue > 0 && barricadeDoseInR > 0 && !distanceInMeters.isNaN() && !distanceInMeters.isInfinite()) {
            saveBarricadeHistory(barricadeDoseInput, selectedDoseUnit, numExposures, distanceInMeters)
        }
    }

    private fun setupHvlTypeSpinner(prefs: SharedPreferences) {
        val hvlTypes = listOf("Standard 95", "Without", "Custom") // Definované typy HVL
        val currentSelectedHvlType = prefs.getString("selected_hvl_type_exposure", hvlTypes.firstOrNull())

        setupSpinner(binding.spinnerHvlType, hvlTypes, currentSelectedHvlType) { selectedType ->
            prefs.edit().putString("selected_hvl_type_exposure", selectedType).apply()
            // Zde se určí, která hodnota HVL se má použít pro výpočty
            val hvlValueToUse = when (selectedType) {
                "Standard 95" -> dataRepo.hvls.firstOrNull { it.nazev == "Standard 95" }?.hodnota?.toString() ?: "0.0"
                "Without" -> "0.0"
                "Custom" -> prefs.getString("custom_hvl_value", "0.0") ?: "0.0"
                else -> "0.0"
            }
            prefs.edit().putString("current_hvl_value_for_calculation", hvlValueToUse).apply()
            calculateBarricadeWithHistory() // Přepočítat bariéru při změně typu HVL
        }
        // Nastavení počáteční hodnoty pro výpočet
        val initialHvlValueToUse = when (currentSelectedHvlType) {
            "Standard 95" -> dataRepo.hvls.firstOrNull { it.nazev == "Standard 95" }?.hodnota?.toString() ?: "0.0"
            "Without" -> "0.0"
            "Custom" -> prefs.getString("custom_hvl_value", "0.0") ?: "0.0"
            else -> "0.0"
        }
        prefs.edit().putString("current_hvl_value_for_calculation", initialHvlValueToUse).apply()
    }

    // Pomocné metody pro správu paměti input polí
    private fun loadSavedInputValues(prefs: SharedPreferences) {
        // Načtení uložených hodnot do input polí
        binding.inputThickness.setText(prefs.getString(PREF_THICKNESS, ""))
        binding.inputWeldReinforcement.setText(prefs.getString(PREF_WELD_REINFORCEMENT, ""))
        binding.inputDistance.setText(prefs.getString(PREF_DISTANCE, ""))
        binding.inputUserFactor.setText(prefs.getString(PREF_USER_FACTOR, ""))
        binding.inputNumExposures.setText(prefs.getString(PREF_NUM_EXPOSURES, ""))
        binding.inputBarricadeDose.setText(prefs.getString(PREF_BARRICADE_DOSE, ""))
    }

    private fun setupInputFieldWithMemory(
        editText: android.widget.EditText,
        prefKey: String,
        prefs: SharedPreferences,
        additionalAction: (() -> Unit)? = null
    ) {
        editText.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                // Uložit hodnotu do SharedPreferences
                prefs.edit().putString(prefKey, s.toString()).apply()

                // Spustit výpočet expozice
                calculateAndDisplayExposure()

                // Spustit dodatečnou akci (např. calculateBarricadeWithHistory)
                additionalAction?.invoke()
            }
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        })
    }

    private fun updateUnitSystemDisplay() {
        val prefs = PreferenceManager.getDefaultSharedPreferences(requireContext())
        val unitSystem = prefs.getString("unit_system", "Metrický") ?: "Metrický"
        android.util.Log.d("ExposureCalc", "Aktuální systém jednotek: $unitSystem")

        // Pokusit se najít TextView pomocí view
        val warningTextView = view?.findViewById<TextView>(R.id.textUnitWarning)
        if (warningTextView != null) {
            val unitInfo = when (unitSystem) {
                "Metrický" -> "📏 Jednotky: mm, GBq/Ci"
                "Americký" -> "📏 Jednotky: palce, Ci"
                "Britský" -> "📏 Jednotky: palce, Ci"
                else -> "📏 Jednotky: $unitSystem"
            }
            warningTextView.text = "$unitInfo | ⚠️ Zkontrolujte v Settings!"
        }
    }

    private fun setupExpandableCard() {
        val prefs = PreferenceManager.getDefaultSharedPreferences(requireContext())

        // Nastavení první karty (zdroj/isotop)
        setupSingleExpandableCard(
            R.id.cardHeaderSource,
            R.id.cardContentSource,
            R.id.iconExpandSource,
            "source_card_expanded",
            true, // Výchozí: rozbaleno
            prefs
        )

        // Nastavení druhé karty (film/koeficienty)
        setupSingleExpandableCard(
            R.id.cardHeaderFilm,
            R.id.cardContentFilm,
            R.id.iconExpandFilm,
            "film_card_expanded",
            false, // Výchozí: sbaleno
            prefs
        )

        // Nastavení třetí karty (bariéra)
        setupSingleExpandableCard(
            R.id.cardHeaderBarricade,
            R.id.cardContentBarricade,
            R.id.iconExpandBarricade,
            "barricade_card_expanded",
            false, // Výchozí: sbaleno
            prefs
        )
    }

    private fun setupSingleExpandableCard(
        headerResId: Int,
        contentResId: Int,
        iconResId: Int,
        prefKey: String,
        defaultExpanded: Boolean,
        prefs: SharedPreferences
    ) {
        val cardHeader = view?.findViewById<View>(headerResId)
        val cardContent = view?.findViewById<View>(contentResId)
        val expandIcon = view?.findViewById<View>(iconResId)

        // Načíst uložený stav karty
        val isExpanded = prefs.getBoolean(prefKey, defaultExpanded)

        // Nastavit počáteční stav
        cardContent?.visibility = if (isExpanded) View.VISIBLE else View.GONE
        expandIcon?.rotation = if (isExpanded) 180f else 0f

        // Nastavit click listener
        cardHeader?.setOnClickListener {
            val isCurrentlyExpanded = cardContent?.visibility == View.VISIBLE
            val newExpanded = !isCurrentlyExpanded

            // Animace rozbalení/sbalení
            if (newExpanded) {
                cardContent?.visibility = View.VISIBLE
                expandIcon?.animate()?.rotation(180f)?.duration = 200
            } else {
                cardContent?.visibility = View.GONE
                expandIcon?.animate()?.rotation(0f)?.duration = 200
            }

            // Uložit stav
            prefs.edit().putBoolean(prefKey, newExpanded).apply()
        }
    }

    // OPRAVA ANR: Metody pro loading a error stavy
    private fun showLoadingState(show: Boolean) {
        if (show) {
            // Zobrazit loading indikátor a skrýt obsah
            binding.textExposureResult.text = getString(R.string.loading_data)
            // Můžete přidat ProgressBar pokud je v layoutu
        }
        // Při skrytí loading stavu se obsah automaticky zobrazí
    }

    private fun showErrorState() {
        binding.textExposureResult.text = getString(R.string.error_loading_data)
        // Zobrazit chybovou zprávu uživateli
        android.widget.Toast.makeText(
            requireContext(),
            getString(R.string.error_loading_data),
            android.widget.Toast.LENGTH_LONG
        ).show()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        countdownTimer?.cancel()
        _binding = null
    }
}
