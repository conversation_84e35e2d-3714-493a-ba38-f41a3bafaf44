package com.example.stopky

import android.os.Bundle
import android.view.Menu
import com.google.android.material.snackbar.Snackbar
import com.google.android.material.navigation.NavigationView
import androidx.navigation.findNavController
import androidx.navigation.ui.AppBarConfiguration
import androidx.navigation.ui.navigateUp
import androidx.navigation.ui.setupActionBarWithNavController
import androidx.navigation.ui.setupWithNavController
import androidx.drawerlayout.widget.DrawerLayout
import androidx.appcompat.app.AppCompatActivity
import com.example.stopky.databinding.ActivityMainBinding
import android.content.Intent
import androidx.navigation.ui.NavigationUI
import android.view.MenuItem

class MainActivity : AppCompatActivity() {

    private lateinit var appBarConfiguration: AppBarConfiguration
    private lateinit var binding: ActivityMainBinding

    // Funkce pro načtení JSON souboru z assets
    private fun loadJsonFromAssets(fileName: String): String {
        return assets.open(fileName).bufferedReader().use { it.readText() }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        val prefs = android.preference.PreferenceManager.getDefaultSharedPreferences(this)
        val lang = prefs.getString("app_language", "cs") ?: "cs"
        setAppLocale(this, lang)

        super.onCreate(savedInstanceState)

        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setSupportActionBar(binding.appBarMain.toolbar)

        val navController = findNavController(R.id.nav_host_fragment_content_main)
        navController.addOnDestinationChangedListener { _, destination, _ ->
            when (destination.id) {
                    R.id.nav_gallery, R.id.nav_exposurecalculation, R.id.nav_iqiselection, R.id.nav_exposurecorrection,  R.id.nav_decay, R.id.nav_unsharp, R.id.nav_stopwatch, R.id.nav_countdown,  R.id.nav_matcalc,  R.id.nav_settings -> binding.appBarMain.fab.hide()
                else -> binding.appBarMain.fab.show()
            }
        }

        binding.navView.setNavigationItemSelectedListener { menuItem ->
            when (menuItem.itemId) {
                R.id.nav_settings -> {
                    val navController = findNavController(R.id.nav_host_fragment_content_main)
                    navController.navigate(R.id.nav_settings)
                    binding.drawerLayout.closeDrawers()
                    true
                }
                else -> {
                    val navController = findNavController(R.id.nav_host_fragment_content_main)
                    val handled = NavigationUI.onNavDestinationSelected(menuItem, navController)
                    if (handled) binding.drawerLayout.closeDrawers()
                    handled
                }
            }
        }

        binding.appBarMain.fab.setOnClickListener { view ->
            Snackbar.make(view, "Replace with your own action", Snackbar.LENGTH_LONG)
                .setAction("Action", null)
                .setAnchorView(R.id.fab).show()
        }
        val drawerLayout: DrawerLayout = binding.drawerLayout
        val navView: NavigationView = binding.navView
        appBarConfiguration = AppBarConfiguration(
            setOf(
                R.id.nav_home
                //, R.id.nav_gallery, R.id.nav_slideshow, R.id.nav_decay, R.id.nav_unsharp, R.id.nav_stopwatch, R.id.nav_countdown,  R.id.nav_matcalc
            ), drawerLayout
        )
        setupActionBarWithNavController(navController, appBarConfiguration)

        // OPRAVA ANR: Odebrán synchronní načítání JSON dat z hlavního vlákna
        // JSON data se nyní načítají asynchronně v jednotlivých fragmentech podle potřeby
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        // Inflate the menu; this adds items to the action bar if it is present.
        menuInflater.inflate(R.menu.main, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_settings -> {
                val navController = findNavController(R.id.nav_host_fragment_content_main)
                navController.navigate(R.id.nav_settings)
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    override fun onSupportNavigateUp(): Boolean {
        val navController = findNavController(R.id.nav_host_fragment_content_main)
        return navController.navigateUp(appBarConfiguration) || super.onSupportNavigateUp()
    }
}